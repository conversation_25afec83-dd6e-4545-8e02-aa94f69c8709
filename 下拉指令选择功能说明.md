# 🎮 GM控制台下拉指令选择功能（带滚动条版本）

## 🆕 新增功能
为GM控制台添加了带滚动条的下拉式指令选择功能，现在你不需要记住所有指令了！

## 🎯 功能特点

### 📋 智能下拉菜单
- **位置**：输入框右侧的"选择指令 ▼"按钮
- **尺寸**：140x160像素（包含滚动条），完美适配屏幕
- **样式**：深色主题，与控制台风格一致
- **滚动支持**：最多显示8个选项，超出部分可滚动查看

### 🎨 交互体验
- **点击展开**：点击下拉按钮展开指令列表
- **鼠标悬停**：悬停在指令上会高亮显示
- **一键选择**：点击指令自动填入输入框
- **自动关闭**：选择指令后自动关闭下拉菜单
- **智能关闭**：点击控制台其他区域自动关闭菜单

### 🖱️ 滚动功能
- **鼠标滚轮**：在下拉菜单上滚动鼠标滚轮快速浏览
- **拖拽滚动条**：拖拽右侧滚动条精确定位
- **智能显示**：只有当选项超过8个时才显示滚动条
- **平滑滚动**：流畅的滚动动画体验

### 📝 预设指令列表
1. **显示帮助** - `help`
2. **上帝模式** - `god`
3. **添加99999金币** - `gold 99999`
4. **添加50000点券** - `ticket 50000`
5. **设置99级** - `level 99`
6. **设置VIP10级** - `vip 10`
7. **添加物品** - `item 10001 999`
8. **绕过作弊检测** - `nocheat`
9. **保存游戏** - `save`
10. **清空控制台** - `clear`

## 🚀 使用方法

### 步骤1：打开控制台
- 按 `~` 键打开GM控制台

### 步骤2：使用下拉菜单
1. 点击输入框右侧的"选择指令 ▼"按钮
2. 浏览可用的指令列表（最多显示8个）
3. 如果有更多指令，使用以下方式滚动：
   - **鼠标滚轮**：在菜单上滚动查看更多选项
   - **拖拽滚动条**：拖拽右侧滚动条快速定位
4. 点击你想要的指令
5. 指令会自动填入输入框
6. 按回车键执行指令

### 步骤3：自定义修改（可选）
- 选择指令后，你可以在输入框中修改参数
- 例如：选择"添加99999金币"后，可以改成"gold 5000000"

## 🔧 技术实现

### 修改的文件
- `scripts/mogames/gameUI/dataTool/DataToolModule.as`

### 新增的功能组件
1. **命令数组**：`_commands` - 存储所有预定义指令
2. **下拉按钮**：`_commandDropdown` - 主下拉按钮
3. **容器组件**：`_dropdownContainer` - 下拉菜单主容器
4. **列表组件**：`_dropdownList` - 指令选项列表
5. **遮罩组件**：`_dropdownMask` - 控制可视区域
6. **滚动条**：`_scrollBar` - 滚动条背景
7. **滚动滑块**：`_scrollThumb` - 可拖拽的滚动滑块
8. **状态管理**：多个变量跟踪滚动位置和菜单状态

### 新增的函数
- `createCommandDropdown()` - 创建下拉按钮和容器
- `createScrollBar()` - 创建滚动条组件
- `createDropdownItems()` - 创建指令选项
- `toggleDropdownList()` - 切换下拉菜单显示
- `onItemMouseOver()` - 鼠标悬停效果
- `onItemMouseOut()` - 鼠标离开效果
- `onItemClick()` - 指令选择处理
- `onConsolePanelClick()` - 控制台点击处理
- `onScrollMouseDown()` - 滚动条拖拽开始
- `onScrollMouseMove()` - 滚动条拖拽移动
- `onScrollMouseUp()` - 滚动条拖拽结束
- `onMouseWheel()` - 鼠标滚轮事件
- `updateScrollPosition()` - 更新列表滚动位置
- `updateScrollThumb()` - 更新滚动条滑块位置

## 💡 设计亮点

### 用户友好
- **记忆辅助**：不需要记住复杂的指令
- **快速选择**：一键选择常用指令
- **参数提示**：每个指令都有描述说明

### 界面优化
- **空间利用**：输入框缩小为650px，为下拉菜单留出140px空间（包含滚动条）
- **视觉统一**：下拉菜单采用与控制台一致的深色主题
- **响应式**：鼠标悬停和点击都有视觉反馈
- **屏幕适配**：固定高度160px，确保不会超出屏幕范围

### 交互逻辑
- **智能关闭**：点击其他区域自动关闭菜单，避免界面混乱
- **焦点管理**：选择指令后自动聚焦到输入框
- **光标定位**：自动将光标移到输入框末尾，方便继续编辑

## 🎉 使用建议

### 新手用户
- 直接使用下拉菜单选择指令，简单快捷
- 推荐先选择"上帝模式"体验所有功能

### 高级用户
- 使用下拉菜单作为指令模板，然后自定义参数
- 可以组合使用多个指令实现复杂操作

### 常用组合
1. `god` → 开启上帝模式
2. `save` → 保存游戏进度
3. `gold 9999999` → 添加大量金币
4. `level 99` → 快速升级

现在你可以更轻松地使用GM控制台了！🎮✨
