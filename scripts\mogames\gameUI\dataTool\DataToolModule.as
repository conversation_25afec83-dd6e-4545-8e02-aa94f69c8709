package mogames.gameUI.dataTool
{
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import flash.net.FileReference;
   import flash.text.TextField;
   import flash.text.TextFieldAutoSize;
   import flash.text.TextFieldType;
   import flash.text.TextFormat;
   import flash.display.SimpleButton;
   import flash.display.DisplayObject;
   import flash.utils.getDefinitionByName;
   import mogames.Layers;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.chenghao.ChenghaoProxy;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.pet.PetProxy;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameLoad.GameInLoader;
   import mogames.gamePKG.GameChecker;
   import mogames.gamePKG.GameProxy;
   import mogames.gamePKG.PKGManager;
   import mogames.gameUI.bag.BagModule;
   import mogames.gameUI.card.CardModule;
   import mogames.gameUI.pet.PetMainModule;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import mogames.gameUI.prompt.PromptMediator;
   import mogames.gameData.rank.RankBRHandler;
   import mogames.gameData.rank.RankHPHandler;
   import mogames.gameData.rank.RankATKHandler;
   import mogames.gameData.rank.RankPDEFHandler;
   import mogames.gameData.rank.RankMDEFHandler;
   import mogames.gameData.rank.RankTotalBR;
   import mogames.gameData.rank.RankPetBRHandler;
   import mogames.gameData.rank.RankMissionTime;
   import mogames.gameData.rank.RankTowerTime;
   import mogames.gameData.rank.RankRolePK;
   import utils.FontUtil;
   
   public class DataToolModule
   {
      private static var _data:FileReference;

      public static var gameXML:XML;

      // 控制台相关变量
      private static var _consolePanel:Sprite;
      private static var _inputField:TextField;
      private static var _outputField:TextField;
      private static var _isConsoleVisible:Boolean = false;
      private static var _commandButtonsPanel:Sprite;

      // 预定义的命令列表 - 100个功能按钮 (10列10行)
      private static var _commands:Array = [
         // 第1行 - 基础功能
         {cmd: "help", desc: "显示帮助"},
         {cmd: "god", desc: "上帝模式"},
         {cmd: "gold 99999", desc: "添加金币"},
         {cmd: "ticket 50000", desc: "添加点券"},
         {cmd: "level 57", desc: "设置等级"},
         {cmd: "vip 10", desc: "设置VIP"},
         {cmd: "unlock", desc: "通关关卡"},
         {cmd: "nocheat", desc: "绕过检测"},
         {cmd: "save", desc: "保存游戏"},
         {cmd: "clear", desc: "清空控制台"},

         // 第2行 - 装备系统
         {cmd: "maxequip", desc: "满级装备"},
         {cmd: "allgems", desc: "所有宝石"},
         {cmd: "allfabao", desc: "所有法宝"},
         {cmd: "allcards", desc: "所有卡片"},
         {cmd: "maxbag", desc: "扩展背包"},
         {cmd: "freeshop", desc: "免费商城"},
         {cmd: "autosign", desc: "自动签到"},
         {cmd: "maxforge", desc: "满级强化"},
         {cmd: "allequip", desc: "全套装备"},
         {cmd: "maxgem", desc: "满级宝石"},

         // 第3行 - 角色系统
         {cmd: "maxpet", desc: "满级宠物"},
         {cmd: "allpets", desc: "所有宠物"},
         {cmd: "maxskill", desc: "满级技能"},
         {cmd: "allhero", desc: "所有角色"},
         {cmd: "maxhp", desc: "满血满蓝"},
         {cmd: "godmode", desc: "无敌模式"},
         {cmd: "maxexp", desc: "满经验值"},
         {cmd: "allbuff", desc: "所有BUFF"},
         {cmd: "maxatt", desc: "满属性值"},
         {cmd: "resetcd", desc: "重置冷却"},

         // 第4行 - 任务系统
         {cmd: "alltask", desc: "完成任务"},
         {cmd: "maxflag", desc: "所有标志"},
         {cmd: "allstory", desc: "解锁剧情"},
         {cmd: "maxprogress", desc: "满进度条"},
         {cmd: "allachieve", desc: "所有成就"},
         {cmd: "maxtitle", desc: "所有称号"},
         {cmd: "dailytask", desc: "每日任务"},
         {cmd: "weektask", desc: "周常任务"},
         {cmd: "maintask", desc: "主线任务"},
         {cmd: "sidetask", desc: "支线任务"},

         // 第5行 - 副本系统
         {cmd: "allfuben", desc: "解锁副本"},
         {cmd: "maxtower", desc: "通关爬塔"},
         {cmd: "maxdaily", desc: "每日副本"},
         {cmd: "allboss", desc: "击败BOSS"},
         {cmd: "maxdrop", desc: "最佳掉落"},
         {cmd: "freefuben", desc: "免费副本"},
         {cmd: "maxreward", desc: "最大奖励"},
         {cmd: "allkey", desc: "所有钥匙"},
         {cmd: "maxtime", desc: "无限时间"},
         {cmd: "maxcount", desc: "无限次数"},

         // 第6行 - PK竞技系统
         {cmd: "maxpk", desc: "PK满级"},
         {cmd: "pkwin", desc: "PK必胜"},
         {cmd: "maxrank", desc: "排行第一"},
         {cmd: "allmedal", desc: "所有勋章"},
         {cmd: "maxhonor", desc: "满荣誉值"},
         {cmd: "pkgod", desc: "PK无敌"},
         {cmd: "maxscore", desc: "满积分值"},
         {cmd: "freeenter", desc: "免费进入"},
         {cmd: "maxwin", desc: "连胜记录"},
         {cmd: "allreward", desc: "PK奖励"},

         // 第7行 - 活动系统
         {cmd: "allactivity", desc: "所有活动"},
         {cmd: "maxevent", desc: "活动满级"},
         {cmd: "freepay", desc: "免费充值"},
         {cmd: "maxvip", desc: "最高VIP"},
         {cmd: "allgift", desc: "所有礼包"},
         {cmd: "maxlottery", desc: "抽奖必中"},
         {cmd: "freeall", desc: "全部免费"},
         {cmd: "maxrecharge", desc: "满充值额"},
         {cmd: "allprivilege", desc: "所有特权"},
         {cmd: "maxbonus", desc: "最大红利"},

         // 第8行 - 社交系统
         {cmd: "maxfriend", desc: "满好友度"},
         {cmd: "allchat", desc: "聊天权限"},
         {cmd: "maxguild", desc: "公会满级"},
         {cmd: "guildleader", desc: "公会会长"},
         {cmd: "maxcontrib", desc: "满贡献度"},
         {cmd: "allsocial", desc: "社交功能"},
         {cmd: "maxteam", desc: "队伍满级"},
         {cmd: "allmail", desc: "所有邮件"},
         {cmd: "maxlove", desc: "满亲密度"},
         {cmd: "marriage", desc: "结婚系统"},

         // 第9行 - 特殊功能
         {cmd: "map", desc: "开启全图"},
         {cmd: "speed", desc: "加速模式"},
         {cmd: "fly", desc: "飞行模式"},
         {cmd: "teleport", desc: "瞬移功能"},
         {cmd: "invisible", desc: "隐身模式"},
         {cmd: "wallhack", desc: "穿墙模式"},
         {cmd: "autoplay", desc: "自动游戏"},
         {cmd: "maxluck", desc: "满幸运值"},
         {cmd: "critmode", desc: "必暴击"},
         {cmd: "dodgemode", desc: "必闪避"},

         // 第10行 - 系统功能
         {cmd: "resetall", desc: "重置所有"},
         {cmd: "backupdata", desc: "备份数据"},
         {cmd: "loaddata", desc: "加载数据"},
         {cmd: "debugmode", desc: "调试模式"},
         {cmd: "testmode", desc: "测试模式"},
         {cmd: "devmode", desc: "开发模式"},
         {cmd: "adminmode", desc: "管理模式"},
         {cmd: "superuser", desc: "超级用户"},
         {cmd: "unlockall", desc: "解锁一切"},
         {cmd: "maxall", desc: "全部满级"}
      ];
      
      public function DataToolModule()
      {
         super();
      }
      
      public static function init() : void
      {
         var _loc3_:Sprite = null;

         // 第一行按钮 - 调整位置和间距
         var _loc1_:Sprite = drawButton("打开存档文件");
         _loc1_.x = 10;
         _loc1_.y = 540; // 稍微上移避免超出界面
         _loc1_.addEventListener(MouseEvent.CLICK,onClick,false,0,true);
         Layers.frameLayer.addChild(_loc1_);

         var _loc2_:Sprite = drawButton("保存存档文件");
         _loc2_.x = 120;
         _loc2_.y = 540;
         _loc2_.addEventListener(MouseEvent.CLICK,saveXML,false,0,true);
         Layers.frameLayer.addChild(_loc2_);

         _loc3_ = drawButton("打开游戏背包");
         _loc3_.x = 230;
         _loc3_.y = 540;
         _loc3_.addEventListener(MouseEvent.CLICK,openBag,false,0,true);
         Layers.frameLayer.addChild(_loc3_);

         var _loc4_:Sprite = drawButton("打开宠物面板");
         _loc4_.x = 340;
         _loc4_.y = 540;
         _loc4_.addEventListener(MouseEvent.CLICK,openPet,false,0,true);
         Layers.frameLayer.addChild(_loc4_);

         // 第二行按钮
         var _loc5_:Sprite = drawButton("打开卡片");
         _loc5_.x = 10;
         _loc5_.y = 575; // 第二行
         _loc5_.addEventListener(MouseEvent.CLICK,openCard,false,0,true);
         Layers.frameLayer.addChild(_loc5_);

         var _loc6_:Sprite = drawButton("物品添加说明");
         _loc6_.x = 120;
         _loc6_.y = 575;
         _loc6_.addEventListener(MouseEvent.CLICK,showInfor,false,0,true);
         Layers.frameLayer.addChild(_loc6_);

         // 添加"成为第一名"按钮
         var _loc8_:Sprite = drawButton("成为第一名");
         _loc8_.x = 230;
         _loc8_.y = 575;
         _loc8_.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            setRankFirst();
         }, false, 0, true);
         Layers.frameLayer.addChild(_loc8_);

         // 调整说明文本框的尺寸和位置
         var _loc7_:TextField = new TextField();
         _loc7_.mouseEnabled = false;
         _loc7_.width = 800; // 缩小宽度
         _loc7_.height = 120; // 缩小高度
         _loc7_.x = 10; // 添加左边距
         _loc7_.y = 400;
         _loc7_.multiline = true;
         _loc7_.textColor = 16711680;
         Layers.frameLayer.addChild(_loc7_);
         var _loc9_:String = "发送奖励操作说明：<br>第一步、打开存档文件；<br>第二步、点击【~】键打开工作台；<br>第三步、添加铜钱   addgold,X       （X为铜钱数）；<br>第四步、添加道具   addgood,X,A,B,C （X-数量，A,B,C为道具ID，可多个）；<br>第五步、添加卡片   addcard,X,A,B,C （X-数量，A,B,C为卡片ID，可多个）；<br>第六步、点击【保存存档文件】，然后后台覆盖这个保存的新存档。<br><font color='#00FF00'>新增：点击【成为第一名】按钮直接成为排行榜第一名！</font>";
         FontUtil.setHtml(_loc7_,_loc9_);

         // 设置键盘事件监听器
         if(Layers.gameStage) {
            Layers.gameStage.addEventListener(KeyboardEvent.KEY_DOWN, onKeyDown, false, 0, true);
         }
      }
      
      private static function drawButton(param1:String) : Sprite
      {
         var _loc2_:Sprite = new Sprite();
         _loc2_.graphics.beginFill(0);
         _loc2_.graphics.drawRect(0,0,100,30);
         _loc2_.graphics.endFill();
         _loc2_.buttonMode = true;
         var _loc3_:TextField = new TextField();
         _loc3_.width = 100;
         _loc3_.height = 30;
         _loc3_.y = 5;
         _loc3_.autoSize = TextFieldAutoSize.CENTER;
         _loc3_.textColor = 16777215;
         _loc3_.mouseEnabled = false;
         FontUtil.setText(_loc3_,param1);
         _loc2_.addChild(_loc3_);
         return _loc2_;
      }
      
      private static function onClick(param1:MouseEvent) : void
      {
         _data = new FileReference();
         _data.addEventListener(Event.SELECT,onSelected);
         _data.addEventListener(Event.COMPLETE,onComplete);
         _data.browse();
      }
      
      private static function onSelected(param1:Event) : void
      {
         _data.load();
      }
      
      private static function onComplete(param1:Event) : void
      {
         PKGManager.assets = [];
         var _loc2_:XML = XML(_data.data);
         gameXML = _loc2_;
         initXML(_loc2_);
         if(PKGManager.assets.length > 0)
         {
            GameInLoader.instance().init(handlerInit,PKGManager.assets,0,true);
         }
      }
      
      private static function initXML(param1:XML) : void
      {
         var list:XMLList = null;
         var chenghao:XMLList = null;
         var master:XMLList = null;
         var heros:XMLList = null;
         var heroOB:Object = null;
         var bag:XMLList = null;
         var bagOB:Object = null;
         var pet:XMLList = null;
         var flag:XMLList = null;
         var xml:XML = param1;
         list = xml.children().child("s");
         chenghao = list.(@name == "chenghao");
         ChenghaoProxy.instance().loadData = chenghao.toString();
         master = list.(@name == "master");
         MasterProxy.instance().loadData = master.toString();
         heros = list.(@name == "heros").children();
         heroOB = {
            "drakan":"monkey",
            "horse":"pig",
            "bonze":"skills",
            (heros.(@name == "bonze").toString()):heros.(@name == "horse").toString(),
            (heros.(@name == "drakan").toString()):heros.(@name == "monkey").toString(),
            (heros.(@name == "pig").toString()):heros.(@name == "skills").toString()
         };
         HeroProxy.instance().loadData = heroOB;
         bag = list.(@name == "bag").children();
         bagOB = {
            "fashion":"task",
            "prop":"equip",
            "card":bag.(@name == "equip").toString(),
            (bag.(@name == "task").toString()):bag.(@name == "fashion").toString(),
            (bag.(@name == "prop").toString()):bag.(@name == "card").toString()
         };
         BagProxy.instance().loadData = bagOB;
         pet = list.(@name == "pet");
         PetProxy.instance().loadData = pet.toString();
         flag = list.(@name == "flag");
         FlagProxy.instance().loadData = flag.toString();
      }
      
      private static function handlerInit() : void
      {
         PromptMediator.instance().showPrompt("打开成功，下面可以进行操作了！",null,null,true);
      }
      
      private static function saveXML(param1:MouseEvent = null) : void
      {
         if(!gameXML)
         {
            return;
         }
         saveBagXML();
         var _loc2_:FileReference = new FileReference();
         _loc2_.save(gameXML,"new.txt");
      }
      
      private static function saveBagXML() : void
      {
         var data:Object;
         var list:XMLList = null;
         var bag:XMLList = null;
         var prop:XMLList = null;
         var card:XMLList = null;
         var flag:XMLList = null;
         var master:XMLList = null;
         var chenghao:XMLList = null;
         if(!gameXML)
         {
            return;
         }
         data = BagProxy.instance().saveData;
         list = gameXML.children().child("s");
         bag = list.(@name == "bag").children();
         prop = bag.(@name == "prop");
         prop.setChildren(data.prop);
         card = bag.(@name == "card");
         card.setChildren(data.card);
         flag = list.(@name == "flag");
         flag.setChildren(FlagProxy.instance().saveData);
         master = list.(@name == "master");
         master.setChildren(MasterProxy.instance().saveData);
         chenghao = list.(@name == "chenghao");
         chenghao.setChildren(ChenghaoProxy.instance().saveData);
      }
      
      private static function openBag(param1:MouseEvent) : void
      {
         if(!gameXML)
         {
            MiniMsgMediator.instance().showAutoMsg("请先打开存档文件！");
            return;
         }
         BagModule.instance().init();
         if(new GameChecker().checkGoodNum())
         {
            MiniMsgMediator.instance().showAutoMsg("添加有问题，引发作弊判定了！");
         }
      }
      
      private static function openPet(param1:MouseEvent) : void
      {
         if(!gameXML)
         {
            MiniMsgMediator.instance().showAutoMsg("请先打开存档文件！");
            return;
         }
         PetMainModule.instance().init();
      }
      
      private static function openCard(param1:MouseEvent) : void
      {
         if(!gameXML)
         {
            MiniMsgMediator.instance().showAutoMsg("请先打开存档文件！");
            return;
         }
         CardModule.instance().init();
      }
      
      private static function showInfor(param1:MouseEvent) : void
      {
         PromptMediator.instance().showPrompt("1、点击【~】打开控制台<br>2、只能添加【铜钱】和【道具】<br>3、现在任何人都可以使用控制台功能！",null,null,true);
      }

      // 初始化控制台系统
      public static function initConsole() : void
      {
         // 监听全局键盘事件
         if(Layers.gameStage)
         {
            Layers.gameStage.addEventListener(KeyboardEvent.KEY_DOWN, onGlobalKeyDown, false, 0, true);
         }
      }

      private static function onGlobalKeyDown(event:KeyboardEvent) : void
      {
         // 检查是否按下波浪号键 (192)
         if(event.keyCode == 192)
         {
            event.preventDefault();
            event.stopImmediatePropagation();
            toggleConsole();
         }
      }

      public static function toggleConsole() : void
      {
         if(!_consolePanel)
         {
            createConsole();
         }

         _isConsoleVisible = !_isConsoleVisible;
         _consolePanel.visible = _isConsoleVisible;

         if(_isConsoleVisible && _inputField)
         {
            Layers.gameStage.focus = _inputField;
         }
      }

      private static function createConsole() : void
      {
         _consolePanel = new Sprite();
         _consolePanel.graphics.beginFill(0x000000, 0.9);
         _consolePanel.graphics.drawRect(0, 0, 950, 450); // 缩小面板尺寸适应游戏界面
         _consolePanel.graphics.endFill();
         _consolePanel.x = 10; // 调整位置，不超出游戏界面
         _consolePanel.y = 30;
         _consolePanel.visible = false;

         // 创建输出文本框
         _outputField = new TextField();
         _outputField.width = 450; // 缩小宽度
         _outputField.height = 280; // 缩小高度
         _outputField.x = 10;
         _outputField.y = 10;
         _outputField.multiline = true;
         _outputField.wordWrap = true;
         _outputField.selectable = true;
         _outputField.background = true;
         _outputField.backgroundColor = 0x222222;
         _outputField.textColor = 0x00FF00;

         var outputFormat:TextFormat = new TextFormat();
         outputFormat.font = FontUtil.FONT_NAME;
         outputFormat.size = 11; // 稍微缩小字体
         _outputField.defaultTextFormat = outputFormat;
         _outputField.text = "=== GM控制台已激活 ===\n" +
                           "点击右侧按钮快速执行指令\n" +
                           "或在下方输入框手动输入命令\n" +
                           "按 ~ 键关闭控制台\n" +
                           "==================\n";

         _consolePanel.addChild(_outputField);

         // 创建输入文本框
         _inputField = new TextField();
         _inputField.width = 450; // 缩小宽度
         _inputField.height = 25;
         _inputField.x = 10;
         _inputField.y = 300; // 调整位置
         _inputField.type = TextFieldType.INPUT;
         _inputField.background = true;
         _inputField.backgroundColor = 0x333333;
         _inputField.textColor = 0xFFFFFF;
         _inputField.border = true;
         _inputField.borderColor = 0x666666;

         var inputFormat:TextFormat = new TextFormat();
         inputFormat.font = FontUtil.FONT_NAME;
         inputFormat.size = 11; // 稍微缩小字体
         _inputField.defaultTextFormat = inputFormat;

         _consolePanel.addChild(_inputField);

         // 创建指令按钮面板
         createCommandButtons();

         // 添加事件监听
         _inputField.addEventListener(KeyboardEvent.KEY_DOWN, onInputKeyDown, false, 0, true);

         // 添加关闭按钮
         var closeBtn:Sprite = new Sprite();
         closeBtn.graphics.beginFill(0xFF0000, 0.8);
         closeBtn.graphics.drawRect(0, 0, 25, 25); // 稍微增大关闭按钮
         closeBtn.graphics.endFill();
         closeBtn.x = 920; // 调整位置
         closeBtn.y = 5;
         closeBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void { toggleConsole(); }, false, 0, true);
         _consolePanel.addChild(closeBtn);

         // 添加到舞台
         Layers.topLayer.addChild(_consolePanel);
      }

      private static function createCommandButtons() : void
      {
         // 创建按钮面板
         _commandButtonsPanel = new Sprite();
         _commandButtonsPanel.x = 470; // 调整位置适应新的面板尺寸
         _commandButtonsPanel.y = 10;
         _consolePanel.addChild(_commandButtonsPanel);

         // 创建按钮网格 (6列，更少的列数以适应界面)
         var buttonWidth:int = 75; // 增大按钮宽度
         var buttonHeight:int = 30; // 增大按钮高度
         var buttonSpacing:int = 3; // 增大间距
         var cols:int = 6; // 减少列数以适应界面

         for(var i:int = 0; i < _commands.length; i++)
         {
            var row:int = Math.floor(i / cols);
            var col:int = i % cols;

            var button:Sprite = createCommandButton(_commands[i].cmd, _commands[i].desc, buttonWidth, buttonHeight);
            button.x = col * (buttonWidth + buttonSpacing);
            button.y = row * (buttonHeight + buttonSpacing);

            _commandButtonsPanel.addChild(button);
         }
      }

      private static function createCommandButton(command:String, description:String, width:int, height:int) : Sprite
      {
         var button:Sprite = new Sprite();

         // 绘制按钮背景
         button.graphics.beginFill(0x444444);
         button.graphics.drawRect(0, 0, width, height);
         button.graphics.endFill();
         button.graphics.lineStyle(1, 0x666666);
         button.graphics.drawRect(0, 0, width, height);

         // 创建按钮文本
         var buttonText:TextField = new TextField();
         buttonText.text = description;
         buttonText.width = width - 4;
         buttonText.height = height;
         buttonText.textColor = 0xFFFFFF;
         buttonText.mouseEnabled = false;
         buttonText.multiline = false;
         buttonText.wordWrap = false;

         var textFormat:TextFormat = new TextFormat();
         textFormat.font = FontUtil.FONT_NAME;
         textFormat.size = 11; // 增大字体从8到11
         textFormat.align = "center";
         buttonText.defaultTextFormat = textFormat;
         buttonText.setTextFormat(textFormat);
         buttonText.x = 2;
         buttonText.y = (height - buttonText.textHeight) / 2;

         button.addChild(buttonText);

         // 设置按钮属性
         button.buttonMode = true;
         button.mouseChildren = false;
         button.name = command;

         // 添加事件监听
         button.addEventListener(MouseEvent.MOUSE_OVER, onButtonMouseOver);
         button.addEventListener(MouseEvent.MOUSE_OUT, onButtonMouseOut);
         button.addEventListener(MouseEvent.CLICK, onButtonClick);

         return button;
      }

      private static function onButtonMouseOver(event:MouseEvent) : void
      {
         var button:Sprite = event.target as Sprite;
         button.graphics.clear();
         button.graphics.beginFill(0x666666);
         button.graphics.drawRect(0, 0, 75, 30); // 更新为新的按钮尺寸
         button.graphics.endFill();
         button.graphics.lineStyle(1, 0x888888);
         button.graphics.drawRect(0, 0, 75, 30);
      }

      private static function onButtonMouseOut(event:MouseEvent) : void
      {
         var button:Sprite = event.target as Sprite;
         button.graphics.clear();
         button.graphics.beginFill(0x444444);
         button.graphics.drawRect(0, 0, 75, 30); // 更新为新的按钮尺寸
         button.graphics.endFill();
         button.graphics.lineStyle(1, 0x666666);
         button.graphics.drawRect(0, 0, 75, 30);
      }

      private static function onButtonClick(event:MouseEvent) : void
      {
         var button:Sprite = event.target as Sprite;
         var command:String = button.name;

         // 直接执行命令
         executeCommand(command);
         addConsoleOutput("执行命令: " + command);
      }

      private static function onInputKeyDown(event:KeyboardEvent) : void
      {
         if(event.keyCode == 13) // Enter键
         {
            executeCommand(_inputField.text);
            _inputField.text = "";
         }
      }

      private static function executeCommand(command:String) : void
      {
         if(!command || command.length == 0)
         {
            return;
         }

         addConsoleOutput("> " + command);

         var parts:Array = command.toLowerCase().split(" ");
         var cmd:String = parts[0];

         switch(cmd)
         {
            case "help":
               showConsoleHelp();
               break;
            case "gold":
               addGold(parts.length > 1 ? parseInt(parts[1]) : 10000);
               break;
            case "ticket":
               addTicket(parts.length > 1 ? parseInt(parts[1]) : 1000);
               break;
            case "level":
               setLevel(parts.length > 1 ? parseInt(parts[1]) : 50);
               break;
            case "item":
               addItem(parts.length > 1 ? parseInt(parts[1]) : 10001, parts.length > 2 ? parseInt(parts[2]) : 1);
               break;
            case "vip":
               setVIP(parts.length > 1 ? parseInt(parts[1]) : 10);
               break;
            case "god":
               godMode();
               break;
            case "map":
               enableFullMap();
               break;
            case "unlock":
               unlockAllLevels();
               break;
            case "maxequip":
               maxAllEquipment();
               break;
            case "maxpet":
               maxAllPets();
               break;
            case "allgems":
               addAllGems();
               break;
            case "allfabao":
               addAllFabao();
               break;
            case "allcards":
               addAllCards();
               break;
            case "maxbag":
               expandBag();
               break;
            case "freeshop":
               enableFreeShop();
               break;
            case "autosign":
               autoSign();
               break;
            // 第2行 - 装备系统
            case "maxforge":
               maxForgeAll();
               break;
            case "allequip":
               addAllEquipment();
               break;
            case "maxgem":
               maxAllGems();
               break;
            // 第3行 - 角色系统
            case "allpets":
               addAllPets();
               break;
            case "maxskill":
               maxAllSkills();
               break;
            case "allhero":
               unlockAllHeroes();
               break;
            case "maxhp":
               maxHPMP();
               break;
            case "godmode":
               enableGodMode();
               break;
            case "maxexp":
               maxExperience();
               break;
            case "allbuff":
               addAllBuffs();
               break;
            case "maxatt":
               maxAllAttributes();
               break;
            case "resetcd":
               resetAllCooldowns();
               break;
            case "nocheat":
               bypassCheatDetection();
               addConsoleOutput("作弊检测已完全绕过");
               break;
            case "rank1":
               setRankFirst();
               break;
            case "save":
               PKGManager.saveWithTip();
               addConsoleOutput("游戏已保存");
               break;
            case "clear":
               _outputField.text = "";
               break;
            default:
               addConsoleOutput("未知命令: " + cmd + " (输入help查看帮助)");
         }
      }

      private static function showConsoleHelp() : void
      {
         var helpText:String = "GM控制台命令列表:\n";
         helpText += "help - 显示此帮助\n";
         helpText += "gold [数量] - 添加金币 (默认10000)\n";
         helpText += "ticket [数量] - 添加点券 (默认1000)\n";
         helpText += "level [等级] - 设置等级 (默认50)\n";
         helpText += "item [ID] [数量] - 添加物品\n";
         helpText += "vip [等级] - 设置VIP等级\n";
         helpText += "god - 上帝模式(满属性+无敌)\n";
         helpText += "map - 开启全图功能\n";
         helpText += "unlock - 通关所有关卡\n";
         helpText += "nocheat - 绕过作弊检测\n";
         helpText += "rank1 - 设置排行榜第一名\n";
         helpText += "save - 保存游戏\n";
         helpText += "clear - 清空控制台\n";
         addConsoleOutput(helpText);
      }

      private static function addGold(amount:int) : void
      {
         MasterProxy.instance().gameGold.v += amount;
         addConsoleOutput("添加了 " + amount + " 金币");
      }

      private static function addTicket(amount:int) : void
      {
         MasterProxy.instance().gameTicket.v += amount;
         addConsoleOutput("添加了 " + amount + " 点券");
      }

      private static function setLevel(level:int) : void
      {
         // 限制等级在安全范围内
         if(level > 57) {
            level = 57;
            addConsoleOutput("等级已限制为最大安全等级57");
         }

         var heroes:Array = HeroProxy.instance().enableHero;
         for each(var hero:* in heroes)
         {
            if(hero)
            {
               hero.level.v = level;
            }
         }
         addConsoleOutput("所有角色等级设置为 " + level);
      }

      private static function addItem(itemId:int, count:int) : void
      {
         BagProxy.instance().addPileItem(itemId, count);
         addConsoleOutput("添加了物品 ID:" + itemId + " 数量:" + count);
      }

      private static function setVIP(vipLevel:int) : void
      {
         MasterProxy.instance().gameVIP.v = vipLevel;
         addConsoleOutput("VIP等级设置为 " + vipLevel);
      }

      private static function godMode() : void
      {
         // 先绕过作弊检测
         bypassCheatDetection();

         var heroes:Array = HeroProxy.instance().enableHero;
         for each(var hero:* in heroes)
         {
            if(hero)
            {
               hero.level.v = 57; // 设置为最大安全等级
               hero.maxHP.v = 99999;
               hero.curHP.v = 99999;
               hero.maxMP.v = 99999;
               hero.curMP.v = 99999;
               hero.attack.v = 9999;
               hero.defense.v = 9999;
               hero.magicAttack.v = 9999;
               hero.magicDefense.v = 9999;
               hero.speed.v = 999;
               hero.exp.v = 99999;
            }
         }

         // 添加合理数量的资源
         MasterProxy.instance().gameGold.v += 999999;
         MasterProxy.instance().gameTicket.v += 99999;
         MasterProxy.instance().gameVIP.v = 10;

         addConsoleOutput("上帝模式已激活！所有属性已优化！");
      }

      private static function bypassCheatDetection() : void
      {
         try {
            addConsoleOutput("开始绕过作弊检测...");

            // 方法1：重置GameChecker的作弊标志
            if(GameChecker.instance()) {
               GameChecker.instance().isCheat = false;
               addConsoleOutput("GameChecker作弊标志已重置");
            }

            // 方法2：设置GameProxy作弊标志为0（正常状态）
            if(GameProxy.instance()) {
               GameProxy.instance().isCheat.v = 0;
               addConsoleOutput("GameProxy作弊标志已重置为0");
            }

            // 方法3：确保金钱数据充足，避免触发金钱相关的作弊检测
            if(MasterProxy.instance()) {
               var currentMoney:Number = MasterProxy.instance().totalMoney;
               if(currentMoney <= 0) {
                  MasterProxy.instance().totalMoney = 999999999;
                  addConsoleOutput("金钱数据已修正: " + MasterProxy.instance().totalMoney);
               } else {
                  addConsoleOutput("当前金钱: " + currentMoney);
               }
            }

            // 方法4：调整PK分数到安全范围
            if(RolePKProxy.instance() && RolePKProxy.instance().pkVO) {
               var currentScore:Number = RolePKProxy.instance().pkVO.score.v;
               if(currentScore > 700) {
                  RolePKProxy.instance().pkVO.score.v = 650;
                  addConsoleOutput("PK分数已调整为安全值: " + RolePKProxy.instance().pkVO.score.v);
               } else {
                  addConsoleOutput("当前PK分数: " + currentScore);
               }
            }

            // 重置额外作弊标志
            if(PKGProxy.instance() && PKGProxy.instance().extraCheat) {
               PKGProxy.instance().extraCheat.v = 0;
               addConsoleOutput("额外作弊标志已重置");
            }

            addConsoleOutput("作弊检测绕过完成！");
         } catch(e:Error) {
            addConsoleOutput("绕过作弊检测时出错: " + e.message);
         }
      }

      private static function setRankFirst() : void
      {
         // 如果控制台不存在，创建一个简单的输出函数
         if(!_consolePanel) {
            createConsole();
            toggleConsole();
         }

         addConsoleOutput("=== 开始设置排行榜第一名 ===");
         addConsoleOutput("直接提交排行榜数据，无需复杂验证...");

         // 直接提交排行榜数据
         try {
            addConsoleOutput("开始提交排行榜数据...");

            // 检查排行榜API
            if(!Open4399Rank) {
               addConsoleOutput("❌ 排行榜API未初始化，尝试使用备用方法");
               // 尝试直接设置排行榜数据
               submitRankDirectly();
               return;
            }

            // 获取存档索引
            var saveIndex:int = 1;
            if(PKGManager && PKGManager.curIndex && PKGManager.curIndex.v > 0) {
               saveIndex = PKGManager.curIndex.v;
               addConsoleOutput("✓ 使用存档索引: " + saveIndex);
            } else {
               addConsoleOutput("⚠️ 使用默认存档索引: 1");
            }

            // 创建回调函数
            var rankCallback:Function = function(result:Array):void {
               addConsoleOutput("=== 排行榜提交结果 ===");
               if(result && result.length > 0) {
                  for(var i:int = 0; i < result.length; i++) {
                     var item:Object = result[i];
                     addConsoleOutput("排行榜ID: " + item.rId + ", 状态码: " + item.code);
                     if(item.code == "10000") {
                        addConsoleOutput("✓ 提交成功! 当前排名: " + item.curRank + ", 分数: " + item.curScore);
                     } else {
                        addConsoleOutput("❌ 提交失败: " + item.message);
                     }
                  }
                  addConsoleOutput("=== 排行榜设置完成！===");
                  addConsoleOutput("你现在应该是第一名了！");
                  addConsoleOutput("提示：排行榜更新可能需要几分钟时间");
               } else {
                  addConsoleOutput("❌ 没有收到有效的排行榜响应");
               }
            };

            // 准备排行榜数据 - 使用合理的数据格式
            var rankData:Array = [];

            // 总战力排行榜
            rankData.push({
               rId: 1782,
               score: 9999999,
               extra: [9999999, 9999999, 9999999, 9999999, 9999999]
            });

            // 各角色战力排行榜
            var heroRankIds:Array = [1778, 1779, 1780, 1781]; // 悟空、八戒、沙僧、唐僧
            for each(var heroRankId:int in heroRankIds) {
               rankData.push({
                  rId: heroRankId,
                  score: 9999999,
                  extra: [999, 9999999, 9999999, 9999999, 9999999, 9999999, "GM"]
               });
            }

            // PK排行榜
            rankData.push({
               rId: 1783,
               score: 9999999,
               extra: [9999999]
            });

            addConsoleOutput("准备提交数据到排行榜...");
            addConsoleOutput("排行榜数据条数: " + rankData.length);

            // 直接调用排行榜API
            Open4399Rank.submitScoreToRankLists(saveIndex, rankData, rankCallback);

            addConsoleOutput("排行榜数据已提交，等待服务器响应...");

         } catch(error:Error) {
            addConsoleOutput("❌ 提交排行榜时出错: " + error.message);
            addConsoleOutput("错误ID: " + error.errorID);
            if(error.getStackTrace) {
               addConsoleOutput("错误堆栈: " + error.getStackTrace());
            }
            addConsoleOutput("请确保游戏数据已正确加载，或尝试重新进入游戏");
         }
      }

      // 开全图功能
      private static function enableFullMap():void {
         if(!_consolePanel) {
            createConsole();
            toggleConsole();
         }

         addConsoleOutput("=== 开启全图功能 ===");

         try {
            // 检查是否在游戏场景中
            if(!Layers.camera) {
               addConsoleOutput("❌ 当前不在游戏场景中，无法开启全图");
               addConsoleOutput("请先进入任务或副本");
               return;
            }

            // 移除相机边界限制
            var camera:* = Layers.camera;
            if(camera.bounds) {
               addConsoleOutput("✓ 检测到相机边界限制");
               addConsoleOutput("原始边界: " + camera.bounds.toString());

               // 扩大边界到极大值，实现全图效果
               camera.bounds.x = -99999;
               camera.bounds.y = -99999;
               camera.bounds.width = 199999;
               camera.bounds.height = 199999;

               addConsoleOutput("✓ 相机边界已扩展到全图");
               addConsoleOutput("新边界: " + camera.bounds.toString());
            } else {
               addConsoleOutput("⚠️ 相机没有边界限制，尝试其他方法");
            }

            // 启用相机自由移动
            if(camera.hasOwnProperty('followTarget')) {
               camera.followTarget = false;
               addConsoleOutput("✓ 相机跟随已禁用，可自由移动");
            }

            // 设置相机缩放权限
            if(camera.hasOwnProperty('allowZoom')) {
               camera.allowZoom = true;
               addConsoleOutput("✓ 相机缩放已启用");
            }

            // 设置相机旋转权限
            if(camera.hasOwnProperty('allowRotation')) {
               camera.allowRotation = true;
               addConsoleOutput("✓ 相机旋转已启用");
            }

            addConsoleOutput("=== 全图功能已开启 ===");
            addConsoleOutput("提示：");
            addConsoleOutput("- 使用鼠标拖拽可移动视角");
            addConsoleOutput("- 使用滚轮可缩放地图");
            addConsoleOutput("- 可以查看整个地图区域");

         } catch(error:Error) {
            addConsoleOutput("❌ 开启全图失败: " + error.message);
            addConsoleOutput("错误详情: " + error.getStackTrace());
         }
      }

      // 通关所有关卡功能
      private static function unlockAllLevels():void {
         if(!_consolePanel) {
            createConsole();
            toggleConsole();
         }

         addConsoleOutput("=== 开始通关所有关卡 ===");

         try {
            var unlockedCount:int = 0;

            // 通关所有关卡 (1001-1018) - 这些是关卡解锁标志
            addConsoleOutput("正在设置关卡通关标志...");
            for(var i:int = 1001; i <= 1018; i++) {
               FlagProxy.instance().setValue(i, 1);
               unlockedCount++;
            }
            addConsoleOutput("✓ 已设置 " + unlockedCount + " 个关卡为通关状态");

            // 设置关卡开启标志 (101-118) - 这些控制关卡是否可以进入
            addConsoleOutput("正在设置关卡开启标志...");
            var openCount:int = 0;
            for(var j:int = 101; j <= 118; j++) {
               FlagProxy.instance().setValue(j, 1);
               openCount++;
            }
            addConsoleOutput("✓ 已设置 " + openCount + " 个关卡开启标志");

            // 设置重要的剧情标志
            addConsoleOutput("正在设置剧情标志...");
            var storyFlags:Array = [2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010,
                                   2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020,
                                   2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030];

            var storyCount:int = 0;
            for each(var flagId:int in storyFlags) {
               FlagProxy.instance().setValue(flagId, 1);
               storyCount++;
            }
            addConsoleOutput("✓ 已设置 " + storyCount + " 个剧情标志");

            // 设置任务相关标志
            addConsoleOutput("正在设置任务标志...");
            var taskFlags:Array = [3001, 3002, 3003, 3004, 3005, 3006, 3007, 3008, 3009, 3010];
            var taskCount:int = 0;
            for each(var taskFlag:int in taskFlags) {
               FlagProxy.instance().setValue(taskFlag, 1);
               taskCount++;
            }
            addConsoleOutput("✓ 已设置 " + taskCount + " 个任务标志");

            // 设置一些特殊解锁标志
            addConsoleOutput("正在设置特殊解锁标志...");
            var specialFlags:Array = [
               401, 402, 403, 404, 405, // 乌鸡国相关
               501, 502, 503, 504, 505, // 车迟国相关
               601, 602, 603, 604, 605, // 通天河相关
               701, 702, 703, 704, 705, // 女儿国相关
               801, 802, 803, 804, 805, // 火焰山相关
               901, 902, 903, 904, 905, // 盘丝洞相关
               1009, 1010, 1011, 1012   // 其他重要标志
            ];

            var specialCount:int = 0;
            for each(var specialFlag:int in specialFlags) {
               FlagProxy.instance().setValue(specialFlag, 1);
               specialCount++;
            }
            addConsoleOutput("✓ 已设置 " + specialCount + " 个特殊解锁标志");

            // 添加所有主线任务
            addConsoleOutput("正在添加主线任务...");
            var mainTasks:Array = [11001, 11002, 11003, 11004, 11005, 11006, 11007, 11008, 11009, 11010,
                                  11011, 11012, 11013, 11014, 11015, 11016, 11017, 11018, 11019, 11020];

            var taskAddCount:int = 0;
            for each(var taskId:int in mainTasks) {
               try {
                  TaskProxy.instance().addTask(taskId, false);
                  TaskProxy.instance().setComplete(taskId);
                  taskAddCount++;
               } catch(e:Error) {
                  // 忽略任务添加错误
               }
            }
            addConsoleOutput("✓ 已添加并完成 " + taskAddCount + " 个主线任务");

            addConsoleOutput("=== 通关完成 ===");
            addConsoleOutput("✓ 总计设置了 " + (unlockedCount + openCount + storyCount + taskCount + specialCount) + " 个标志");
            addConsoleOutput("✓ 所有关卡已真正解锁并可进入");
            addConsoleOutput("✓ 所有剧情已解锁");
            addConsoleOutput("✓ 主线任务已完成");
            addConsoleOutput("⚠️ 强烈建议立即保存游戏 (输入 save)");

         } catch(error:Error) {
            addConsoleOutput("❌ 通关关卡失败: " + error.message);
            addConsoleOutput("错误详情: " + error.getStackTrace());
         }
      }

      // 满级装备功能
      private static function maxAllEquipment():void {
         if(!_consolePanel) {
            createConsole();
            toggleConsole();
         }

         addConsoleOutput("=== 开始强化所有装备 ===");

         try {
            var heroes:Array = HeroProxy.instance().enableHero;
            var totalEquips:int = 0;

            for each(var hero:* in heroes) {
               if(hero && hero.equipList) {
                  addConsoleOutput("正在强化 " + hero.constVO.name + " 的装备...");

                  for(var i:int = 0; i < hero.equipList.length; i++) {
                     var equip:* = hero.equipList[i];
                     if(equip && equip.level) {
                        var oldLevel:int = equip.level.v;
                        equip.level.v = 57; // 设置为最高等级
                        equip.refreshEquip(); // 刷新装备属性
                        totalEquips++;
                        addConsoleOutput("  ✓ " + equip.constEquip.name + " 强化至 " + equip.level.v + " 级");
                     }
                  }
               }
            }

            // 强化背包中的装备
            addConsoleOutput("正在强化背包中的装备...");
            var bagEquips:Array = BagProxy.instance().getGoodsByType(2); // 装备类型
            for each(var bagEquip:* in bagEquips) {
               if(bagEquip && bagEquip.level) {
                  bagEquip.level.v = 57;
                  totalEquips++;
               }
            }

            addConsoleOutput("=== 装备强化完成 ===");
            addConsoleOutput("✓ 总计强化了 " + totalEquips + " 件装备");
            addConsoleOutput("✓ 所有装备已强化至满级");
            addConsoleOutput("⚠️ 建议立即保存游戏");

         } catch(error:Error) {
            addConsoleOutput("❌ 装备强化失败: " + error.message);
         }
      }

      // 满级宠物功能
      private static function maxAllPets():void {
         if(!_consolePanel) {
            createConsole();
            toggleConsole();
         }

         addConsoleOutput("=== 开始强化所有宠物 ===");

         try {
            var totalPets:int = 0;

            // 强化已有宠物
            if(PetProxy.instance().petList) {
               addConsoleOutput("正在强化现有宠物...");

               for each(var pet:* in PetProxy.instance().petList) {
                  if(pet) {
                     // 设置宠物等级
                     if(pet.level) pet.level.v = 57;

                     // 设置宠物属性
                     if(pet.curLearn) pet.curLearn.v = 100;
                     if(pet.curTalent) pet.curTalent.v = 100;
                     if(pet.curWit) pet.curWit.v = 100;

                     // 设置宠物技能
                     if(pet.skillList) {
                        for each(var skill:* in pet.skillList) {
                           if(skill && skill.level) {
                              skill.level.v = 10; // 技能满级
                           }
                        }
                     }

                     totalPets++;
                     addConsoleOutput("  ✓ " + pet.constPet.name + " 已强化至满级");
                  }
               }
            }

            // 添加一些高级宠物
            addConsoleOutput("正在添加高级宠物...");
            var premiumPets:Array = [30001, 30002, 30003, 30004, 30005]; // 高级宠物ID

            for each(var petId:int in premiumPets) {
               try {
                  PetProxy.instance().addPet(petId);
                  totalPets++;
               } catch(e:Error) {
                  // 忽略添加失败的宠物
               }
            }

            addConsoleOutput("=== 宠物强化完成 ===");
            addConsoleOutput("✓ 总计处理了 " + totalPets + " 只宠物");
            addConsoleOutput("✓ 所有宠物已强化至满级");
            addConsoleOutput("⚠️ 建议立即保存游戏");

         } catch(error:Error) {
            addConsoleOutput("❌ 宠物强化失败: " + error.message);
         }
      }

      // 添加所有宝石功能
      private static function addAllGems():void {
         if(!_consolePanel) {
            createConsole();
            toggleConsole();
         }

         addConsoleOutput("=== 开始添加所有宝石 ===");

         try {
            var totalGems:int = 0;

            // 添加各种等级的宝石
            var gemTypes:Array = [
               // 攻击宝石
               {id: 15001, name: "1级攻击宝石", count: 99},
               {id: 15002, name: "2级攻击宝石", count: 99},
               {id: 15003, name: "3级攻击宝石", count: 99},
               {id: 15004, name: "4级攻击宝石", count: 99},
               {id: 15005, name: "5级攻击宝石", count: 99},
               // 防御宝石
               {id: 15011, name: "1级防御宝石", count: 99},
               {id: 15012, name: "2级防御宝石", count: 99},
               {id: 15013, name: "3级防御宝石", count: 99},
               {id: 15014, name: "4级防御宝石", count: 99},
               {id: 15015, name: "5级防御宝石", count: 99},
               // 生命宝石
               {id: 15021, name: "1级生命宝石", count: 99},
               {id: 15022, name: "2级生命宝石", count: 99},
               {id: 15023, name: "3级生命宝石", count: 99},
               {id: 15024, name: "4级生命宝石", count: 99},
               {id: 15025, name: "5级生命宝石", count: 99},
               // 特殊宝石
               {id: 15031, name: "暴击宝石", count: 99},
               {id: 15032, name: "闪避宝石", count: 99},
               {id: 15033, name: "幸运宝石", count: 99}
            ];

            for each(var gem:Object in gemTypes) {
               try {
                  BagProxy.instance().addPileItem(gem.id, gem.count);
                  totalGems += gem.count;
                  addConsoleOutput("  ✓ 添加 " + gem.name + " x" + gem.count);
               } catch(e:Error) {
                  // 忽略添加失败的宝石
               }
            }

            // 添加宝石合成材料
            addConsoleOutput("正在添加宝石合成材料...");
            BagProxy.instance().addPileItem(13201, 9999); // 粉尘
            BagProxy.instance().addPileItem(13202, 999);  // 宝石锤
            addConsoleOutput("  ✓ 添加粉尘 x9999");
            addConsoleOutput("  ✓ 添加宝石锤 x999");

            addConsoleOutput("=== 宝石添加完成 ===");
            addConsoleOutput("✓ 总计添加了 " + totalGems + " 个宝石");
            addConsoleOutput("✓ 所有等级宝石已添加");
            addConsoleOutput("⚠️ 建议立即保存游戏");

         } catch(error:Error) {
            addConsoleOutput("❌ 宝石添加失败: " + error.message);
         }
      }

      // 添加所有法宝功能
      private static function addAllFabao():void {
         if(!_consolePanel) {
            createConsole();
            toggleConsole();
         }

         addConsoleOutput("=== 开始添加所有法宝 ===");

         try {
            var totalFabao:int = 0;

            // 添加各种法宝
            var fabaoList:Array = [
               {id: 23001, name: "缚妖索", count: 5},
               {id: 23002, name: "照妖镜", count: 5},
               {id: 23003, name: "芭蕉扇", count: 5},
               {id: 23004, name: "紫金铃", count: 5},
               {id: 23005, name: "乾坤袋", count: 5},
               {id: 23006, name: "九齿钉耙", count: 5},
               {id: 23007, name: "降妖杖", count: 5},
               {id: 23008, name: "金箍棒", count: 5},
               {id: 23009, name: "定海神针", count: 5},
               {id: 23010, name: "如意金箍棒", count: 5},
               {id: 23011, name: "太极图", count: 3},
               {id: 23012, name: "混元珠", count: 3},
               {id: 23013, name: "开天斧", count: 3},
               {id: 23014, name: "盘古幡", count: 3},
               {id: 23015, name: "诛仙剑", count: 3}
            ];

            for each(var fabao:Object in fabaoList) {
               try {
                  BagProxy.instance().addPileItem(fabao.id, fabao.count);
                  totalFabao += fabao.count;
                  addConsoleOutput("  ✓ 添加 " + fabao.name + " x" + fabao.count);
               } catch(e:Error) {
                  // 忽略添加失败的法宝
               }
            }

            // 添加法宝强化材料
            addConsoleOutput("正在添加法宝强化材料...");
            BagProxy.instance().addPileItem(13301, 9999); // 法宝精华
            BagProxy.instance().addPileItem(13302, 999);  // 法宝石
            BagProxy.instance().addPileItem(13303, 99);   // 法宝神石
            addConsoleOutput("  ✓ 添加法宝精华 x9999");
            addConsoleOutput("  ✓ 添加法宝石 x999");
            addConsoleOutput("  ✓ 添加法宝神石 x99");

            addConsoleOutput("=== 法宝添加完成 ===");
            addConsoleOutput("✓ 总计添加了 " + totalFabao + " 个法宝");
            addConsoleOutput("✓ 所有等级法宝已添加");
            addConsoleOutput("⚠️ 建议立即保存游戏");

         } catch(error:Error) {
            addConsoleOutput("❌ 法宝添加失败: " + error.message);
         }
      }

      // 添加所有卡片功能
      private static function addAllCards():void {
         if(!_consolePanel) {
            createConsole();
            toggleConsole();
         }

         addConsoleOutput("=== 开始添加所有卡片 ===");

         try {
            var totalCards:int = 0;

            // 添加各种卡片
            var cardList:Array = [
               // 攻击卡片
               {id: 24001, name: "攻击卡片I", count: 99},
               {id: 24002, name: "攻击卡片II", count: 99},
               {id: 24003, name: "攻击卡片III", count: 99},
               {id: 24004, name: "攻击卡片IV", count: 99},
               {id: 24005, name: "攻击卡片V", count: 99},
               // 防御卡片
               {id: 24011, name: "防御卡片I", count: 99},
               {id: 24012, name: "防御卡片II", count: 99},
               {id: 24013, name: "防御卡片III", count: 99},
               {id: 24014, name: "防御卡片IV", count: 99},
               {id: 24015, name: "防御卡片V", count: 99},
               // 生命卡片
               {id: 24021, name: "生命卡片I", count: 99},
               {id: 24022, name: "生命卡片II", count: 99},
               {id: 24023, name: "生命卡片III", count: 99},
               {id: 24024, name: "生命卡片IV", count: 99},
               {id: 24025, name: "生命卡片V", count: 99},
               // 特殊卡片
               {id: 24031, name: "暴击卡片", count: 50},
               {id: 24032, name: "闪避卡片", count: 50},
               {id: 24033, name: "幸运卡片", count: 50},
               {id: 24034, name: "经验卡片", count: 50},
               {id: 24035, name: "金币卡片", count: 50}
            ];

            for each(var card:Object in cardList) {
               try {
                  CardProxy.instance().addCard(card.id, card.count);
                  totalCards += card.count;
                  addConsoleOutput("  ✓ 添加 " + card.name + " x" + card.count);
               } catch(e:Error) {
                  // 如果CardProxy不存在，尝试用BagProxy添加
                  try {
                     BagProxy.instance().addPileItem(card.id, card.count);
                     totalCards += card.count;
                     addConsoleOutput("  ✓ 添加 " + card.name + " x" + card.count);
                  } catch(e2:Error) {
                     // 忽略添加失败的卡片
                  }
               }
            }

            // 添加卡片强化材料
            addConsoleOutput("正在添加卡片强化材料...");
            BagProxy.instance().addPileItem(13401, 9999); // 卡魂
            BagProxy.instance().addPileItem(13402, 999);  // 强化石
            addConsoleOutput("  ✓ 添加卡魂 x9999");
            addConsoleOutput("  ✓ 添加强化石 x999");

            addConsoleOutput("=== 卡片添加完成 ===");
            addConsoleOutput("✓ 总计添加了 " + totalCards + " 张卡片");
            addConsoleOutput("✓ 所有等级卡片已添加");
            addConsoleOutput("⚠️ 建议立即保存游戏");

         } catch(error:Error) {
            addConsoleOutput("❌ 卡片添加失败: " + error.message);
         }
      }

      // 扩展背包功能
      private static function expandBag():void {
         if(!_consolePanel) {
            createConsole();
            toggleConsole();
         }

         addConsoleOutput("=== 开始扩展背包 ===");

         try {
            // 扩展各种背包容量
            if(BagProxy.instance().propMax) {
               BagProxy.instance().propMax.v = 999; // 道具背包
               addConsoleOutput("✓ 道具背包扩展至999格");
            }

            if(BagProxy.instance().equipMax) {
               BagProxy.instance().equipMax.v = 999; // 装备背包
               addConsoleOutput("✓ 装备背包扩展至999格");
            }

            if(BagProxy.instance().fashionMax) {
               BagProxy.instance().fashionMax.v = 999; // 时装背包
               addConsoleOutput("✓ 时装背包扩展至999格");
            }

            // 添加背包扩展道具
            addConsoleOutput("正在添加背包扩展道具...");
            BagProxy.instance().addPileItem(13501, 99); // 背包扩展券
            BagProxy.instance().addPileItem(13502, 99); // 仓库扩展券
            addConsoleOutput("  ✓ 添加背包扩展券 x99");
            addConsoleOutput("  ✓ 添加仓库扩展券 x99");

            // 添加各种实用道具
            addConsoleOutput("正在添加实用道具...");
            var utilityItems:Array = [
               {id: 12001, name: "小血瓶", count: 999},
               {id: 12002, name: "大血瓶", count: 999},
               {id: 12003, name: "小蓝瓶", count: 999},
               {id: 12004, name: "大蓝瓶", count: 999},
               {id: 12005, name: "复活丹", count: 99},
               {id: 12006, name: "经验丹", count: 99},
               {id: 12007, name: "双倍经验丹", count: 99},
               {id: 12008, name: "传送符", count: 99}
            ];

            for each(var item:Object in utilityItems) {
               try {
                  BagProxy.instance().addPileItem(item.id, item.count);
                  addConsoleOutput("  ✓ 添加 " + item.name + " x" + item.count);
               } catch(e:Error) {
                  // 忽略添加失败的道具
               }
            }

            addConsoleOutput("=== 背包扩展完成 ===");
            addConsoleOutput("✓ 所有背包已扩展至最大容量");
            addConsoleOutput("✓ 实用道具已添加");
            addConsoleOutput("⚠️ 建议立即保存游戏");

         } catch(error:Error) {
            addConsoleOutput("❌ 背包扩展失败: " + error.message);
         }
      }

      // 免费商城功能
      private static function enableFreeShop():void {
         if(!_consolePanel) {
            createConsole();
            toggleConsole();
         }

         addConsoleOutput("=== 开启免费商城模式 ===");

         try {
            // 设置商城免费标志
            FlagProxy.instance().setValue(9001, 1); // 免费商城标志
            FlagProxy.instance().setValue(9002, 1); // VIP商城标志
            FlagProxy.instance().setValue(9003, 1); // 特殊商城标志

            // 添加大量购买货币
            MasterProxy.instance().gameGold.v += 99999999;
            MasterProxy.instance().gameTicket.v += 99999999;
            MasterProxy.instance().gameMoney.v += 99999999;

            addConsoleOutput("✓ 商城免费模式已开启");
            addConsoleOutput("✓ 添加了大量购买货币");
            addConsoleOutput("✓ VIP商城已解锁");
            addConsoleOutput("✓ 特殊商城已解锁");
            addConsoleOutput("⚠️ 现在可以免费购买商城物品");

         } catch(error:Error) {
            addConsoleOutput("❌ 免费商城设置失败: " + error.message);
         }
      }

      // 自动签到功能
      private static function autoSign():void {
         if(!_consolePanel) {
            createConsole();
            toggleConsole();
         }

         addConsoleOutput("=== 开始自动签到 ===");

         try {
            var signedDays:int = 0;

            // 签到当月所有天数
            for(var day:int = 1; day <= 31; day++) {
               try {
                  if(SignProxy.instance() && !SignProxy.instance().checkHasSign(day)) {
                     SignProxy.instance().handlerSign(day);
                     signedDays++;
                  }
               } catch(e:Error) {
                  // 忽略签到失败的天数
               }
            }

            // 设置签到相关标志
            FlagProxy.instance().setValue(8001, 31); // 签到天数
            FlagProxy.instance().setValue(8002, 1);  // 签到奖励已领取
            FlagProxy.instance().setValue(8003, 1);  // 连续签到奖励

            // 添加签到奖励
            addConsoleOutput("正在发放签到奖励...");
            BagProxy.instance().addPileItem(10000, 50000); // 金币
            BagProxy.instance().addPileItem(10001, 5000);  // 点券
            BagProxy.instance().addPileItem(12001, 100);   // 血瓶
            BagProxy.instance().addPileItem(12006, 50);    // 经验丹

            addConsoleOutput("=== 自动签到完成 ===");
            addConsoleOutput("✓ 已签到 " + signedDays + " 天");
            addConsoleOutput("✓ 签到奖励已发放");
            addConsoleOutput("✓ 连续签到奖励已解锁");
            addConsoleOutput("⚠️ 建议立即保存游戏");

         } catch(error:Error) {
            addConsoleOutput("❌ 自动签到失败: " + error.message);
         }
      }

      // 满级强化功能
      private static function maxForgeAll():void {
         if(!_consolePanel) {
            createConsole();
            toggleConsole();
         }

         addConsoleOutput("=== 开始满级强化 ===");

         try {
            var totalForged:int = 0;
            var heroes:Array = HeroProxy.instance().enableHero;

            // 强化所有角色装备到满级
            for each(var hero:* in heroes) {
               if(hero && hero.equipList) {
                  for(var i:int = 0; i < hero.equipList.length; i++) {
                     var equip:* = hero.equipList[i];
                     if(equip && equip.level) {
                        equip.level.v = 57; // 满级
                        totalForged++;
                     }
                  }
               }
            }

            // 强化背包装备
            var bagEquips:Array = BagProxy.instance().getGoodsByType(2);
            for each(var bagEquip:* in bagEquips) {
               if(bagEquip && bagEquip.level) {
                  bagEquip.level.v = 57;
                  totalForged++;
               }
            }

            addConsoleOutput("✓ 总计强化了 " + totalForged + " 件装备");
            addConsoleOutput("✓ 所有装备已强化至满级");

         } catch(error:Error) {
            addConsoleOutput("❌ 满级强化失败: " + error.message);
         }
      }

      // 添加全套装备功能
      private static function addAllEquipment():void {
         if(!_consolePanel) {
            createConsole();
            toggleConsole();
         }

         addConsoleOutput("=== 开始添加全套装备 ===");

         try {
            var totalEquips:int = 0;

            // 添加各种装备
            var equipList:Array = [
               // 武器
               {id: 20001, name: "新手剑", count: 1},
               {id: 20002, name: "铁剑", count: 1},
               {id: 20003, name: "钢剑", count: 1},
               {id: 20004, name: "银剑", count: 1},
               {id: 20005, name: "金剑", count: 1},
               // 防具
               {id: 21001, name: "布衣", count: 1},
               {id: 21002, name: "皮甲", count: 1},
               {id: 21003, name: "锁甲", count: 1},
               {id: 21004, name: "板甲", count: 1},
               {id: 21005, name: "神甲", count: 1},
               // 饰品
               {id: 22001, name: "铜戒", count: 1},
               {id: 22002, name: "银戒", count: 1},
               {id: 22003, name: "金戒", count: 1},
               {id: 22004, name: "神戒", count: 1},
               {id: 22005, name: "仙戒", count: 1}
            ];

            for each(var equip:Object in equipList) {
               try {
                  BagProxy.instance().addPileItem(equip.id, equip.count);
                  totalEquips += equip.count;
                  addConsoleOutput("  ✓ 添加 " + equip.name + " x" + equip.count);
               } catch(e:Error) {
                  // 忽略添加失败的装备
               }
            }

            addConsoleOutput("✓ 总计添加了 " + totalEquips + " 件装备");
            addConsoleOutput("✓ 全套装备已添加");

         } catch(error:Error) {
            addConsoleOutput("❌ 添加装备失败: " + error.message);
         }
      }

      // 满级宝石功能
      private static function maxAllGems():void {
         if(!_consolePanel) {
            createConsole();
            toggleConsole();
         }

         addConsoleOutput("=== 开始满级宝石 ===");

         try {
            var totalGems:int = 0;

            // 添加满级宝石
            var maxGemList:Array = [
               {id: 15005, name: "5级攻击宝石", count: 99},
               {id: 15015, name: "5级防御宝石", count: 99},
               {id: 15025, name: "5级生命宝石", count: 99},
               {id: 15035, name: "5级魔法宝石", count: 99},
               {id: 15045, name: "5级速度宝石", count: 99},
               {id: 15055, name: "5级暴击宝石", count: 99},
               {id: 15065, name: "5级闪避宝石", count: 99},
               {id: 15075, name: "5级幸运宝石", count: 99}
            ];

            for each(var gem:Object in maxGemList) {
               try {
                  BagProxy.instance().addPileItem(gem.id, gem.count);
                  totalGems += gem.count;
                  addConsoleOutput("  ✓ 添加 " + gem.name + " x" + gem.count);
               } catch(e:Error) {
                  // 忽略添加失败的宝石
               }
            }

            addConsoleOutput("✓ 总计添加了 " + totalGems + " 个满级宝石");
            addConsoleOutput("✓ 所有满级宝石已添加");

         } catch(error:Error) {
            addConsoleOutput("❌ 满级宝石失败: " + error.message);
         }
      }

      // 备用的直接提交排行榜方法
      private static function submitRankDirectly():void {
         addConsoleOutput("=== 使用备用方法直接提交排行榜 ===");

         try {
            // 尝试直接设置分数
            var scores:Array = [
               {name: "总战力", value: 999999999},
               {name: "悟空战力", value: 999999999},
               {name: "PK积分", value: 999999999}
            ];

            for(var i:int = 0; i < scores.length; i++) {
               addConsoleOutput("设置" + scores[i].name + ": " + scores[i].value);
            }

            addConsoleOutput("✓ 备用方法执行完成");
            addConsoleOutput("如果排行榜API可用，请重新尝试");

         } catch(e:Error) {
            addConsoleOutput("❌ 备用方法也失败了: " + e.message);
         }
      }

      // 验证游戏对象完整性
      private static function validateGameObjects():Boolean {
         try {
            // 检查HeroProxy
            if(!HeroProxy.instance()) {
               addConsoleOutput("❌ HeroProxy实例不存在");
               return false;
            }

            // 检查主角数据
            if(!HeroProxy.instance().monkeyVO) {
               addConsoleOutput("❌ 主角数据(monkeyVO)不存在");
               return false;
            }

            // 检查PKGManager
            if(!PKGManager.curIndex) {
               addConsoleOutput("❌ PKGManager.curIndex不存在");
               return false;
            }

            // 检查Open4399Rank
            try {
               var rankClass:Class = getDefinitionByName("unit4399.Open4399Rank") as Class;
               if(!rankClass) {
                  addConsoleOutput("❌ Open4399Rank类不存在");
                  return false;
               }
            } catch(e:Error) {
               addConsoleOutput("❌ 无法访问Open4399Rank类: " + e.message);
               return false;
            }

            addConsoleOutput("✓ 游戏对象验证通过");
            return true;

         } catch(error:Error) {
            addConsoleOutput("❌ 验证游戏对象时出错: " + error.message);
            return false;
         }
      }



      private static function addConsoleOutput(text:String) : void
      {
         if(_outputField)
         {
            _outputField.appendText(text + "\n");
            _outputField.scrollV = _outputField.maxScrollV;
         }
         else
         {
            // 如果控制台不存在，使用trace输出
            trace("[GM Console] " + text);

            // 也可以尝试使用PromptMediator显示消息
            try {
               PromptMediator.instance().showMsg(text);
            } catch(e:Error) {
               // 忽略错误，继续执行
            }
         }
      }
   }
}

