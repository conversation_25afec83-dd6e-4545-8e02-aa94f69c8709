# GM控制台使用说明

## 🎮 功能概述
已成功为游戏添加了完整的GM控制台功能，任何人都可以使用，无需任何限制。

## 🔑 开启方法
- **按 `~` 键（波浪号键）** 即可打开/关闭控制台
- 控制台会显示在游戏界面上方
- 支持实时输入命令

## 📝 可用命令列表

### 基础命令
- `help` - 显示所有可用命令
- `clear` - 清空控制台输出
- `save` - 保存游戏

### 资源命令
- `gold [数量]` - 添加金币（默认10000）
  - 例：`gold 999999` 添加999999金币
- `ticket [数量]` - 添加点券（默认1000）
  - 例：`ticket 50000` 添加50000点券

### 角色命令
- `level [等级]` - 设置所有角色等级（默认50）
  - 例：`level 99` 设置所有角色为99级
- `vip [等级]` - 设置VIP等级（默认10）
  - 例：`vip 10` 设置VIP等级为10

### 物品命令
- `item [物品ID] [数量]` - 添加指定物品
  - 例：`item 10001 99` 添加99个ID为10001的物品

### 特殊命令
- `god` - **上帝模式**
  - 所有角色等级99级
  - 生命值/魔法值999999
  - 攻击力/防御力99999
  - 添加大量金币和点券
  - 自动绕过作弊检测
- `nocheat` - 单独绕过作弊检测

## 🛠️ 技术修改详情

### 修改的文件：
1. **scripts/Monkey.as**
   - 设置 `isLocal = true` 启用本地模式
   - 在游戏启动时初始化控制台

2. **scripts/file/KeyConfig.as**
   - 添加波浪号键（~）的键盘监听

3. **scripts/mogames/gamePKG/GameChecker.as**
   - 修改作弊检测逻辑，本地模式下跳过检测
   - 添加Monkey类的引用

4. **scripts/mogames/gameUI/dataTool/DataToolModule.as**
   - 添加完整的控制台系统
   - 实现所有GM命令功能
   - 添加控制台UI界面

### 核心特性：
- ✅ 无需任何前置条件，任何人都可以使用
- ✅ 实时命令执行，立即生效
- ✅ 完整的作弊检测绕过
- ✅ 友好的用户界面
- ✅ 支持批量操作

## 🎯 使用示例

### 快速开始：
1. 启动游戏
2. 按 `~` 键打开控制台
3. 输入 `god` 开启上帝模式
4. 享受游戏！

### 自定义设置：
```
gold 5000000     # 添加500万金币
ticket 100000    # 添加10万点券
level 80         # 设置角色等级为80
vip 8            # 设置VIP等级为8
item 10001 999   # 添加999个特定物品
```

## ⚠️ 注意事项
- 控制台在游戏的任何界面都可以使用
- 命令不区分大小写
- 数值参数支持大数值输入
- 建议在使用GM功能后手动保存游戏

## 🎉 享受游戏！
现在你可以自由地使用所有GM功能，无任何限制地体验游戏内容！
